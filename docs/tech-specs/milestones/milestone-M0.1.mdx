---
title: Milestone M0.1 — Docusaurus Documentation Site
description: Bootstrap a static documentation site (Docusaurus 2) that renders all MDX technical specifications.
created: 2025-01-25
version: 0.1.0
status: Draft
tags: [milestone, documentation]
authors: [nitishMehrotra]
---



<Callout emoji="📚">
<strong>Goal:</strong> Stand-up a fully-working Docusaurus site under <code>code/apps/docs-site/</code>,
wired into CI, so future specs and domain docs are browsable and versioned.
This milestone is infrastructure-only—no parsing logic.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"              # minimum supported
pnpm: "8.15.4"
docusaurus: "3.8.0"          # latest stable - better MDX support
docusaurus-search-local: "0.44.0"   # compatible with Docusaurus 3.x
typescript: "5.4.3"
react: "18.2.0"              # required for Docusaurus 3.x
```

---

## 🎯 Definition of Done

1. `pnpm run docs:build` produces static HTML at `code/apps/docs-site/build/` without errors.
2. `pnpm run docs:start` serves the site locally at <http://localhost:3000>.
3. CI workflow `docs.yml` builds the site on every push & PR.
4. All existing MDX specs render via sidebar navigation and local search.
5. Spec passes `spec-lint` and dry-run gates.

---

## 📦 Deliverables

| Path | Must contain … |
|------|----------------|
| `code/apps/docs-site/` | **Docusaurus project root** (`docusaurus.config.js`, `sidebars.js`, etc.) |
| `code/apps/docs-site/docusaurus.config.js` | Configure `docs.path: '../../docs/tech-specs'` to read existing MDX files. |
| `code/apps/docs-site/tsconfig.json` | Enable TypeScript in MDX components. |
| `code/apps/docs-site/package.json` | Docusaurus dependencies + scripts. |
| Root `package.json` | Workspace scripts: `docs:start`, `docs:build`. |
| `.github/workflows/docs.yml` | CI workflow building docs site. |

---

## 🗂 Directory Layout

```text
code/apps/docs-site/
├── docusaurus.config.js  # Configure docs.path: '../../docs/tech-specs'
├── sidebars.js
├── src/components/       # Callout component reuse
├── static/               # images, logos
├── package.json
└── tsconfig.json

# Existing MDX content (unchanged):
docs/tech-specs/
├── milestones/
├── adrs/
├── domains/
├── process/
└── ...
```

---

## 🧠 Key Decisions

| Topic | Decision | Rationale |
|-------|----------|-----------|
| **Doc engine** | **Docusaurus 3.x** | MDX-native, better parser, React 18 support, improved error handling. |
| **Location** | `code/apps/docs-site` | Keeps docs beside code; configure to read existing MDX files. |
| **Search** | `@easyops-cn/docusaurus-search-local` | Offline/local search; no Algolia creds required. |
| **Deployment** | GitHub Pages default | Zero infra; can migrate later to Netlify/Vercel/S3. |

---

## 🧠 Key Decisions

| Topic | Decision | Rationale |
|-------|----------|-----------|
| **Doc engine** | **Docusaurus 3.x** | MDX-native, better parser, React 18 support, improved error handling. |
| **Location** | `code/apps/docs-site` | Keeps docs beside code; configure to read existing MDX files. |
| **Search** | `@easyops-cn/docusaurus-search-local` | Offline/local search; no Algolia creds required. |
| **Deployment** | GitHub Pages default | Zero infra; can migrate later to Netlify/Vercel/S3. |

---

## ✅ Success Criteria

- [ ] SC-1 `pnpm run docs:build` exits 0 locally.
- [ ] SC-2 `pnpm run docs:start` renders specs at `/tech-spec/milestones/milestone-M0`.
- [ ] SC-3 CI workflow `docs.yml` green on push.
- [ ] SC-4 README badge (to be added) shows docs build status.
- [ ] SC-5 Spec passes checklist lint:
  ```bash
  node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
- [ ] SC-6 All acceptance tests pass:
  ```bash
  bash docs/scripts/acceptance/m0.1-acceptance.sh
  ```

---

## 🔨 Task Breakdown

| #  | Branch name           | Task (issue title)                            | Owner | Hint |
|----|----------------------|-----------------------------------------------|-------|------|
| 01 | `m0.1/init-docus`     | Scaffold Docusaurus site in `code/apps/docs-site` | FE    | `pnpm create docusaurus@3.8.0` |
| 02 | `m0.1/config-path`    | Configure `docs.path: '../../docs/tech-specs'` in config | FE    | Point to existing MDX files |
| 03 | `m0.1/sidebar`        | Configure `sidebars.js` per template rules   | FE    | Milestones, Domains, Template |
| 04 | `m0.1/search`         | Add local search plugin + config             | FE    | Plugin import in config |
| 05 | `m0.1/components`     | Port `<Callout>` component to docs site      | FE    | Copy from codebase or re-create |
| 06 | `m0.1/scripts`        | Add root scripts `docs:start`, `docs:build`  | FE    | Update root package.json |
| 07 | `m0.1/ci-docs`        | Add `.github/workflows/docs.yml`             | DevOps | Build docs on PR |
| 08 | `m0.1/gh-pages`       | Configure GitHub Pages deploy (gh-pages branch) | DevOps | Use `peaceiris/actions-gh-pages` |
| 09 | `m0.1/spec-quality`   | Run spec-lint, mark spec Approved            | PM    | Ensure checklist passes |
| 10 | `m0.1/final-tag`      | Merge, tag `docs-v0.1.0`                     | Lead  | CI green |

---

## 🤖 CI Pipeline

```yaml
name: Docs CI
on:
  push:
    branches: [main]
  pull_request:

jobs:
  build-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm --filter code/apps/docs-site docs:build
      - uses: actions/upload-artifact@v4
        with: { name: docs-site, path: code/apps/docs-site/build }
  deploy:
    if: github.ref == 'refs/heads/main'
    needs: build-docs
    runs-on: ubuntu-latest
    steps:
      - uses: actions/download-artifact@v4
        with: { name: docs-site, path: code/apps/docs-site/build }
      - uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: code/apps/docs-site/build
```

---

## 🧪 Acceptance Tests

### 1️⃣ Local build & serve

```bash
pnpm run docs:build
pnpm run docs:start &
sleep 5
curl -fs http://localhost:3000/tech-spec/structure
# Expect: 200 OK HTML
```

### 2️⃣ Sidebar links

Open browser → verify sidebar shows **Tech Specs → Milestones → Milestone M0**.

### 3️⃣ Search

Hit keyboard `/`, type "Monorepo runner"; first result should link to milestone-M0.

### 4️⃣ CI success

CI workflow completes, artifact uploaded; on `main`, GitHub Pages URL renders site.

---

---

## 🔄 Document History

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 0.1.0 | 2025-01-25 | nitishMehrotra | Initial milestone specification for Docusaurus documentation site |

---

When all criteria pass, merge to **`main`**, tag **`docs-v0.1.0`**, and mark Milestone M0.1 closed.
