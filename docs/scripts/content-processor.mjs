#!/usr/bin/env node

/**
 * Content Processor for Complex MDX Files
 * Automatically transforms complex MDX files into Docusaurus-compatible chunks
 * while preserving user authoring freedom
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Processing thresholds based on M0.1 investigation
const THRESHOLDS = {
  MAX_LINES: 120,           // Conservative limit for reliable builds
  MAX_TABLES: 4,            // Tables are expensive to parse
  SECTION_SPLIT_LINES: 80,  // Split sections at this size
  MIN_SECTION_LINES: 20     // Don't create tiny sections
};

class ContentProcessor {
  constructor(sourceDir, outputDir) {
    this.sourceDir = sourceDir;
    this.outputDir = outputDir;
    this.processedFiles = [];
    this.sidebarEntries = [];
  }

  async processAllFiles() {
    console.log(`🔄 Processing content from ${this.sourceDir} to ${this.outputDir}`);
    
    // Clean output directory
    if (fs.existsSync(this.outputDir)) {
      fs.rmSync(this.outputDir, { recursive: true });
    }
    fs.mkdirSync(this.outputDir, { recursive: true });
    
    // Process all MDX files
    await this.processDirectory(this.sourceDir, '');
    
    // Generate dynamic sidebar
    await this.generateSidebar();
    
    console.log(`✅ Processed ${this.processedFiles.length} files`);
    return this.processedFiles;
  }

  async processDirectory(currentDir, relativePath) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const itemRelativePath = path.join(relativePath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Create directory in output
        const outputDirPath = path.join(this.outputDir, itemRelativePath);
        fs.mkdirSync(outputDirPath, { recursive: true });
        
        // Recursively process subdirectory
        await this.processDirectory(fullPath, itemRelativePath);
      } else if (item.endsWith('.mdx') || item.endsWith('.md')) {
        await this.processFile(fullPath, itemRelativePath);
      } else {
        // Copy non-MDX files as-is
        const outputPath = path.join(this.outputDir, itemRelativePath);
        fs.copyFileSync(fullPath, outputPath);
      }
    }
  }

  async processFile(filePath, relativePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const analysis = this.analyzeContent(content);
    
    console.log(`📄 Processing: ${relativePath}`);
    console.log(`   Lines: ${analysis.lineCount}, Tables: ${analysis.tableCount}`);
    
    if (this.needsSplitting(analysis)) {
      console.log(`   🔀 Splitting into sections (complex content)`);
      await this.splitFile(content, relativePath, analysis);
    } else {
      console.log(`   ✅ Copying as-is (simple content)`);
      await this.copyFile(content, relativePath);
    }
  }

  analyzeContent(content) {
    const lines = content.split('\n');
    const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
    const frontmatterEnd = frontmatterMatch ? frontmatterMatch[0].split('\n').length - 1 : 0;
    
    return {
      lineCount: lines.length,
      tableCount: (content.match(/^\|.*\|$/gm) || []).length / 2,
      frontmatter: frontmatterMatch ? frontmatterMatch[1] : '',
      frontmatterEnd,
      sections: this.extractSections(lines, frontmatterEnd),
      hasComplexTables: this.hasComplexTables(content)
    };
  }

  needsSplitting(analysis) {
    return analysis.lineCount > THRESHOLDS.MAX_LINES || 
           analysis.tableCount > THRESHOLDS.MAX_TABLES ||
           analysis.hasComplexTables;
  }

  hasComplexTables(content) {
    // Detect tables with complex content (nested markdown, long cells)
    const tableRows = content.match(/^\|.*\|$/gm) || [];
    return tableRows.some(row => 
      row.length > 200 || // Very long rows
      row.includes('```') || // Code blocks in tables
      row.includes('[') && row.includes('](') // Links in tables
    );
  }

  extractSections(lines, frontmatterEnd) {
    const sections = [];
    let currentSection = null;
    
    for (let i = frontmatterEnd; i < lines.length; i++) {
      const line = lines[i];
      
      // Detect section headers (## or ###)
      if (line.match(/^#{2,3}\s+/)) {
        // Save previous section
        if (currentSection) {
          sections.push(currentSection);
        }
        
        // Start new section
        currentSection = {
          title: line.replace(/^#{2,3}\s+/, ''),
          slug: this.createSlug(line.replace(/^#{2,3}\s+/, '')),
          startLine: i,
          lines: [line],
          level: line.match(/^#{2,3}/)[0].length
        };
      } else if (currentSection) {
        currentSection.lines.push(line);
      }
    }
    
    // Add final section
    if (currentSection) {
      sections.push(currentSection);
    }
    
    return sections;
  }

  createSlug(title) {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  async splitFile(content, relativePath, analysis) {
    const baseName = path.basename(relativePath, path.extname(relativePath));
    const dirName = path.dirname(relativePath);
    
    // Create main index file
    const indexContent = this.createIndexFile(analysis, baseName);
    const indexPath = path.join(this.outputDir, dirName, `${baseName}.mdx`);
    fs.writeFileSync(indexPath, indexContent);
    
    // Create section files
    const sectionDir = path.join(this.outputDir, dirName, baseName);
    fs.mkdirSync(sectionDir, { recursive: true });
    
    for (const section of analysis.sections) {
      if (section.lines.length >= THRESHOLDS.MIN_SECTION_LINES) {
        const sectionContent = this.createSectionFile(section, analysis.frontmatter);
        const sectionPath = path.join(sectionDir, `${section.slug}.mdx`);
        fs.writeFileSync(sectionPath, sectionContent);
        
        this.sidebarEntries.push({
          type: 'doc',
          id: path.join(dirName, baseName, section.slug).replace(/\\/g, '/'),
          label: section.title
        });
      }
    }
    
    this.processedFiles.push({
      original: relativePath,
      type: 'split',
      sections: analysis.sections.length
    });
  }

  createIndexFile(analysis, baseName) {
    const sections = analysis.sections
      .filter(s => s.lines.length >= THRESHOLDS.MIN_SECTION_LINES)
      .map(s => `- [${s.title}](./${baseName}/${s.slug})`)
      .join('\n');
    
    return `---
title: ${baseName.replace(/-/g, ' ')}
description: Auto-generated index for complex content
generated: true
original_sections: ${analysis.sections.length}
---

# ${baseName.replace(/-/g, ' ')}

This document has been automatically split into sections for optimal rendering:

${sections}

---

*This is an auto-generated index. The original content has been preserved and split for better performance.*
`;
  }

  createSectionFile(section, frontmatter) {
    const simplifiedFrontmatter = this.simplifyFrontmatter(frontmatter);
    
    return `---
${simplifiedFrontmatter}
title: ${section.title}
generated: true
section: true
---

${section.lines.join('\n')}
`;
  }

  simplifyFrontmatter(frontmatter) {
    // Keep only essential frontmatter fields to avoid complexity
    const lines = frontmatter.split('\n');
    const essential = ['title', 'description', 'created', 'status', 'tags'];
    
    return lines
      .filter(line => essential.some(field => line.startsWith(`${field}:`)))
      .join('\n');
  }

  async copyFile(content, relativePath) {
    const outputPath = path.join(this.outputDir, relativePath);
    fs.writeFileSync(outputPath, content);
    
    this.processedFiles.push({
      original: relativePath,
      type: 'copied'
    });
  }

  async generateSidebar() {
    // This would generate a dynamic sidebar based on processed content
    // For now, we'll create a simple mapping file
    const sidebarPath = path.join(this.outputDir, '_sidebar-mapping.json');
    fs.writeFileSync(sidebarPath, JSON.stringify(this.sidebarEntries, null, 2));
  }
}

// CLI execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const sourceDir = process.argv[2] || path.join(__dirname, '../../docs/tech-specs');
  const outputDir = process.argv[3] || path.join(__dirname, '../../code/apps/docs-site/processed-docs');
  
  console.log(`🚀 Starting content processing...`);
  console.log(`📂 Source: ${sourceDir}`);
  console.log(`📁 Output: ${outputDir}`);
  
  if (!fs.existsSync(sourceDir)) {
    console.error(`❌ Source directory not found: ${sourceDir}`);
    process.exit(1);
  }
  
  const processor = new ContentProcessor(sourceDir, outputDir);
  
  try {
    await processor.processAllFiles();
    console.log(`🎉 Content processing completed successfully!`);
    process.exit(0);
  } catch (error) {
    console.error(`❌ Content processing failed:`, error);
    process.exit(1);
  }
}

export default ContentProcessor;
